{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 1300942506884646018, "profile": 8731458305071235362, "path": 10763286916239946207, "deps": [[1636925107938680435, "tauri_plugin_upload", false, 7302156592980855184], [4843756016100095416, "tauri_plugin_updater", false, 17430445058643408297], [4972584477725338812, "tauri_plugin_shell", false, 6303645252703567685], [5986029879202738730, "log", false, 14282795578811856172], [6828091393945420899, "tauri_plugin_positioner", false, 4287094323875589050], [6898646762435821041, "env_logger", false, 822168551512187086], [9689903380558560274, "serde", false, 5802927860388080493], [10806645703491011684, "thiserror", false, 10629514662035982276], [13568641959108680238, "build_script_build", false, 11858686460389519035], [14039947826026167952, "tauri", false, 3661919430774992089], [14177399613791699417, "tauri_plugin_single_instance", false, 8762430041541800548], [14525517306681678134, "tauri_plugin_dialog", false, 1041388647023388430], [15367738274754116744, "serde_json", false, 13366999495009309396], [16171925541490437305, "tauri_plugin_http", false, 17693424963484109807], [17675327481376616781, "encoding", false, 1654675453869314657], [18372475104564266000, "zip", false, 1382652415352167639]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rockoon-06a1c021a0320787\\dep-lib-rockoon_lib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}