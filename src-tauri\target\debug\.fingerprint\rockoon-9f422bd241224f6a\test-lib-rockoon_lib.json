{"rustc": 10895048813736897673, "features": "[]", "declared_features": "[]", "target": 1300942506884646018, "profile": 3316208278650011218, "path": 10763286916239946207, "deps": [[1636925107938680435, "tauri_plugin_upload", false, 2144000830023776565], [4843756016100095416, "tauri_plugin_updater", false, 18277036554734941566], [4972584477725338812, "tauri_plugin_shell", false, 515612490848554138], [5986029879202738730, "log", false, 16057183136734989296], [6828091393945420899, "tauri_plugin_positioner", false, 273969880400660736], [6898646762435821041, "env_logger", false, 14580915453626211025], [9689903380558560274, "serde", false, 15552301292015110326], [10806645703491011684, "thiserror", false, 9025268902796271355], [13568641959108680238, "build_script_build", false, 16310312066917705128], [14039947826026167952, "tauri", false, 9197466790522510022], [14177399613791699417, "tauri_plugin_single_instance", false, 4820623755447096176], [14525517306681678134, "tauri_plugin_dialog", false, 11753654912639838579], [15367738274754116744, "serde_json", false, 12110891527249516773], [16171925541490437305, "tauri_plugin_http", false, 5848102311263839176], [17675327481376616781, "encoding", false, 15998575492132673410], [18372475104564266000, "zip", false, 2786245859494704223]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\rockoon-9f422bd241224f6a\\dep-test-lib-rockoon_lib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}