D:\Codes\rockoon\src-tauri\target\debug\deps\librockoon_lib-1b83eb35b0a98ec5.rmeta: src\lib.rs src\ballance\mod.rs src\ballance\mod_config.rs src\ballance\options.rs src\ballance\tdb\mod.rs src\ballance\tdb\structs.rs src\ballance\tdb\utils.rs src\commands\mod.rs src\commands\app.rs src\commands\ballance.rs src\commands\fs.rs src\commands\process.rs src\common\mod.rs src\common\exception.rs D:\Codes\rockoon\src-tauri\target\debug\build\rockoon-f1b6df747ee649ca\out/175e3dfa2d41b29dfcf408a0ed07e84e28aaf11d91e71bbf246ec77d706f1c2b Cargo.toml

D:\Codes\rockoon\src-tauri\target\debug\deps\rockoon_lib-1b83eb35b0a98ec5.d: src\lib.rs src\ballance\mod.rs src\ballance\mod_config.rs src\ballance\options.rs src\ballance\tdb\mod.rs src\ballance\tdb\structs.rs src\ballance\tdb\utils.rs src\commands\mod.rs src\commands\app.rs src\commands\ballance.rs src\commands\fs.rs src\commands\process.rs src\common\mod.rs src\common\exception.rs D:\Codes\rockoon\src-tauri\target\debug\build\rockoon-f1b6df747ee649ca\out/175e3dfa2d41b29dfcf408a0ed07e84e28aaf11d91e71bbf246ec77d706f1c2b Cargo.toml

src\lib.rs:
src\ballance\mod.rs:
src\ballance\mod_config.rs:
src\ballance\options.rs:
src\ballance\tdb\mod.rs:
src\ballance\tdb\structs.rs:
src\ballance\tdb\utils.rs:
src\commands\mod.rs:
src\commands\app.rs:
src\commands\ballance.rs:
src\commands\fs.rs:
src\commands\process.rs:
src\common\mod.rs:
src\common\exception.rs:
D:\Codes\rockoon\src-tauri\target\debug\build\rockoon-f1b6df747ee649ca\out/175e3dfa2d41b29dfcf408a0ed07e84e28aaf11d91e71bbf246ec77d706f1c2b:
Cargo.toml:

# env-dep:CARGO_PKG_AUTHORS=Ghomist
# env-dep:CARGO_PKG_DESCRIPTION=an all-in-one manager for ballance
# env-dep:CARGO_PKG_NAME=rockoon
# env-dep:CLIPPY_ARGS=
# env-dep:CLIPPY_CONF_DIR
# env-dep:OUT_DIR=D:\\Codes\\rockoon\\src-tauri\\target\\debug\\build\\rockoon-f1b6df747ee649ca\\out
