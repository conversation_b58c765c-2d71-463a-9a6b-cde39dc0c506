D:\Codes\rockoon\src-tauri\target\debug\deps\rockoon_lib.lib: src\lib.rs src\ballance\mod.rs src\ballance\mod_config.rs src\ballance\options.rs src\ballance\tdb\mod.rs src\ballance\tdb\structs.rs src\ballance\tdb\utils.rs src\commands\mod.rs src\commands\app.rs src\commands\ballance.rs src\commands\fs.rs src\commands\process.rs src\common\mod.rs src\common\exception.rs D:\Codes\rockoon\src-tauri\target\debug\build\rockoon-65019706f79452c1\out/175e3dfa2d41b29dfcf408a0ed07e84e28aaf11d91e71bbf246ec77d706f1c2b

D:\Codes\rockoon\src-tauri\target\debug\deps\rockoon_lib.dll: src\lib.rs src\ballance\mod.rs src\ballance\mod_config.rs src\ballance\options.rs src\ballance\tdb\mod.rs src\ballance\tdb\structs.rs src\ballance\tdb\utils.rs src\commands\mod.rs src\commands\app.rs src\commands\ballance.rs src\commands\fs.rs src\commands\process.rs src\common\mod.rs src\common\exception.rs D:\Codes\rockoon\src-tauri\target\debug\build\rockoon-65019706f79452c1\out/175e3dfa2d41b29dfcf408a0ed07e84e28aaf11d91e71bbf246ec77d706f1c2b

D:\Codes\rockoon\src-tauri\target\debug\deps\librockoon_lib.rlib: src\lib.rs src\ballance\mod.rs src\ballance\mod_config.rs src\ballance\options.rs src\ballance\tdb\mod.rs src\ballance\tdb\structs.rs src\ballance\tdb\utils.rs src\commands\mod.rs src\commands\app.rs src\commands\ballance.rs src\commands\fs.rs src\commands\process.rs src\common\mod.rs src\common\exception.rs D:\Codes\rockoon\src-tauri\target\debug\build\rockoon-65019706f79452c1\out/175e3dfa2d41b29dfcf408a0ed07e84e28aaf11d91e71bbf246ec77d706f1c2b

D:\Codes\rockoon\src-tauri\target\debug\deps\rockoon_lib.d: src\lib.rs src\ballance\mod.rs src\ballance\mod_config.rs src\ballance\options.rs src\ballance\tdb\mod.rs src\ballance\tdb\structs.rs src\ballance\tdb\utils.rs src\commands\mod.rs src\commands\app.rs src\commands\ballance.rs src\commands\fs.rs src\commands\process.rs src\common\mod.rs src\common\exception.rs D:\Codes\rockoon\src-tauri\target\debug\build\rockoon-65019706f79452c1\out/175e3dfa2d41b29dfcf408a0ed07e84e28aaf11d91e71bbf246ec77d706f1c2b

src\lib.rs:
src\ballance\mod.rs:
src\ballance\mod_config.rs:
src\ballance\options.rs:
src\ballance\tdb\mod.rs:
src\ballance\tdb\structs.rs:
src\ballance\tdb\utils.rs:
src\commands\mod.rs:
src\commands\app.rs:
src\commands\ballance.rs:
src\commands\fs.rs:
src\commands\process.rs:
src\common\mod.rs:
src\common\exception.rs:
D:\Codes\rockoon\src-tauri\target\debug\build\rockoon-65019706f79452c1\out/175e3dfa2d41b29dfcf408a0ed07e84e28aaf11d91e71bbf246ec77d706f1c2b:

# env-dep:CARGO_PKG_AUTHORS=Ghomist
# env-dep:CARGO_PKG_DESCRIPTION=an all-in-one manager for ballance
# env-dep:CARGO_PKG_NAME=rockoon
# env-dep:OUT_DIR=D:\\Codes\\rockoon\\src-tauri\\target\\debug\\build\\rockoon-65019706f79452c1\\out
