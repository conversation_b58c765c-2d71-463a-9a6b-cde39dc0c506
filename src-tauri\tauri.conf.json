{"$schema": "https://schema.tauri.app/config/2", "productName": "rockoon", "version": "1.5.10", "identifier": "com.ghomist.rockoon", "build": {"beforeDevCommand": "pnpm dev", "devUrl": "http://127.0.0.1:1420", "beforeBuildCommand": "pnpm build", "frontendDist": "../dist"}, "app": {"withGlobalTauri": false, "windows": [{"label": "main", "title": "Rockoon", "decorations": false, "transparent": true, "shadow": false, "width": 1200, "height": 720, "minWidth": 1000, "minHeight": 600}], "security": {"csp": "default-src 'self' ipc: http://ipc.localhost; img-src 'self' asset: http://asset.localhost", "assetProtocol": {"enable": true, "scope": {"allow": ["**"]}}}}, "bundle": {"active": true, "targets": ["nsis"], "createUpdaterArtifacts": true, "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "plugins": {"updater": {"pubkey": "dW50cnVzdGVkIGNvbW1lbnQ6IG1pbmlzaWduIHB1YmxpYyBrZXk6IDRCMUU2REZEMzMzMEY1QTUKUldTbDlUQXovVzBlUzZLYWNxL0Q1SUlFdXZRcVpDalZrQ3U2YnlVNHduVi9COVZUNjJKVk94NmQK", "endpoints": ["https://github.com/Ghomist/Rockoon/releases/latest/download/latest.json"], "windows": {"installMode": "passive"}}}}