/* animations */
@import url("animations/index.css");

@import url("theme/index.css");

:root {
  font-family: "Noto Sans", Inter, Avenir, Helvetica, Arial, sans-serif;
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-text-size-adjust: 100%;
}

:root {
  /* make transparent window */
  background: transparent;
}

body {
  user-select: none;
  -ms-user-select: none;
  -moz-user-select: none;
  -webkit-user-select: none;

  scrollbar-width: none;

  margin: 8px;
  filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.75));

  &.on-maximized {
    margin: 0;
    filter: none;
  }
}

::-webkit-scrollbar {
  display: none;
}

div {
  cursor: default;
  transition: all 200ms;
}

p {
  margin: 0;
}

input {
  outline: none;
  border: 1px solid rgba(128, 128, 128, 0.5);
  border-radius: 8px;
  padding: 8px;
  background-color: rgba(255, 255, 255, 0.6);
}

button {
  outline: none;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
  cursor: pointer;
}

a:hover {
  color: #535bf2;
}

h1 {
  text-align: center;
}

.anim {
  transition: all 200ms;
}

/* @media (prefers-color-scheme: dark) {
  :root {
    color: #f6f6f6;
    background-color: #2f2f2f;
  }

  a:hover {
    color: #24c8db;
  }

  input,
  button {
    color: #ffffff;
    background-color: #0f0f0f98;
  }

  button:active {
    background-color: #0f0f0f69;
  }
} */
