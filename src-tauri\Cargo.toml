[package]
name = "rockoon"
version = "0.1.0"
description = "an all-in-one manager for ballance"
authors = ["Ghomist"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "rockoon_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
encoding = "0.2.33"
tauri = { version = "2", features = [
    "protocol-asset",
    "image-png",
    "devtools",
] }
serde = { version = "1", features = ["derive"] }
serde_json = "1"
tauri-plugin-shell = "^2"
tauri-plugin-dialog = "^2"
tauri-plugin-upload = "^2"
tauri-plugin-http = { version = "^2", features = ["unsafe-headers"] }
zip = "2.2.2"
thiserror = "2.0.12"
log = "0.4.27"
env_logger = "0.11.8"

[target.'cfg(not(any(target_os = "android", target_os = "ios")))'.dependencies]
tauri-plugin-positioner = "2"
tauri-plugin-single-instance = "2"
tauri-plugin-updater = "2"

[profile.dev]
incremental = true

[profile.release]
codegen-units = 1 # Enable LLVM
lto = true
opt-level = 2
panic = "abort"
strip = true
