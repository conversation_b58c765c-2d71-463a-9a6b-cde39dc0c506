:root {
  color: #0f0f0f;
  background-color: #f6f6f6;

  --color-prime: #888;
  --background-image: linear-gradient(
    to right,
    var(--color-prime),
    var(--color-prime)
  );
}

:root.theme-default,
:root.theme-blue {
  --color-prime: #3f79ff;
  --background-image: linear-gradient(120deg, #a1c4fd 0%, #c2e9fb 100%);
}

:root.theme-red {
  --color-prime: #ff553f;
  --background-image: linear-gradient(to right, #fa709a 0%, #fee140 100%);
}

:root.theme-green {
  --color-prime: #2ec943;
  --background-image: linear-gradient(120deg, #d4f587 0%, #82db8e 100%);
}

:root.theme-pink {
  --color-prime: #ff7f94;
  --background-image: linear-gradient(-20deg, #f794a4 0%, #fdd6bd 100%);
}

:root.theme-gray {
  /* filter: contrast(0); */
  --color-prime: #687279;
  --background-image: linear-gradient(to top, #cfd9df 0%, #e2ebf0 100%);
}
