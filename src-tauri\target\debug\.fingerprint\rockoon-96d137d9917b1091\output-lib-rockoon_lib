{"$message_type":"diagnostic","message":"mismatched types","code":{"code":"E0308","explanation":"Expected type did not match the received type.\n\nErroneous code examples:\n\n```compile_fail,E0308\nfn plus_one(x: i32) -> i32 {\n    x + 1\n}\n\nplus_one(\"Not a number\");\n//       ^^^^^^^^^^^^^^ expected `i32`, found `&str`\n\nif \"Not a bool\" {\n// ^^^^^^^^^^^^ expected `bool`, found `&str`\n}\n\nlet x: f32 = \"Not a float\";\n//     ---   ^^^^^^^^^^^^^ expected `f32`, found `&str`\n//     |\n//     expected due to this\n```\n\nThis error occurs when an expression was used in a place where the compiler\nexpected an expression of a different type. It can occur in several cases, the\nmost common being when calling a function and passing an argument which has a\ndifferent type than the matching type in the function declaration.\n"},"level":"error","spans":[{"file_name":"src\\commands\\ballance.rs","byte_start":699,"byte_end":707,"line_start":30,"line_end":30,"column_start":22,"column_end":30,"is_primary":true,"text":[{"text":"    options.write_to(&mut tdb)?;","highlight_start":22,"highlight_end":30}],"label":"expected `&str`, found `&mut Tdb`","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\commands\\ballance.rs","byte_start":690,"byte_end":698,"line_start":30,"line_end":30,"column_start":13,"column_end":21,"is_primary":false,"text":[{"text":"    options.write_to(&mut tdb)?;","highlight_start":13,"highlight_end":21}],"label":"arguments to this method are incorrect","suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[{"message":"     expected reference `&str`\nfound mutable reference `&mut ballance::tdb::structs::Tdb`","code":null,"level":"note","spans":[],"children":[],"rendered":null},{"message":"method defined here","code":null,"level":"note","spans":[{"file_name":"src\\ballance\\options.rs","byte_start":3020,"byte_end":3029,"line_start":100,"line_end":100,"column_start":32,"column_end":41,"is_primary":false,"text":[{"text":"    pub fn write_to(&mut self, tdb: &str) -> RcResult {","highlight_start":32,"highlight_end":41}],"label":"","suggested_replacement":null,"suggestion_applicability":null,"expansion":null},{"file_name":"src\\ballance\\options.rs","byte_start":3000,"byte_end":3008,"line_start":100,"line_end":100,"column_start":12,"column_end":20,"is_primary":true,"text":[{"text":"    pub fn write_to(&mut self, tdb: &str) -> RcResult {","highlight_start":12,"highlight_end":20}],"label":null,"suggested_replacement":null,"suggestion_applicability":null,"expansion":null}],"children":[],"rendered":null}],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror[E0308]\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: mismatched types\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\commands\\ballance.rs:30:22\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m30\u001b[0m\u001b[0m  \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    options.write_to(&mut tdb)?;\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--------\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9m^^^^^^^^\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;9mexpected `&str`, found `&mut Tdb`\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m             \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14marguments to this method are incorrect\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m= \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15mnote\u001b[0m\u001b[0m:      expected reference `&\u001b[0m\u001b[0m\u001b[1m\u001b[35mstr\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m            found mutable reference `&\u001b[0m\u001b[0m\u001b[1m\u001b[35mmut ballance::tdb::structs::Tdb\u001b[0m\u001b[0m`\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;10mnote\u001b[0m\u001b[0m: method defined here\u001b[0m\n\u001b[0m   \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m--> \u001b[0m\u001b[0msrc\\ballance\\options.rs:100:12\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\n\u001b[0m\u001b[1m\u001b[38;5;14m100\u001b[0m\u001b[0m \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m \u001b[0m\u001b[0m    pub fn write_to(&mut self, tdb: &str) -> RcResult {\u001b[0m\n\u001b[0m    \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m|\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;10m^^^^^^^^\u001b[0m\u001b[0m            \u001b[0m\u001b[0m\u001b[1m\u001b[38;5;14m---------\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"aborting due to 1 previous error","code":null,"level":"error","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;9merror\u001b[0m\u001b[0m\u001b[1m\u001b[38;5;15m: aborting due to 1 previous error\u001b[0m\n\n"}
{"$message_type":"diagnostic","message":"For more information about this error, try `rustc --explain E0308`.","code":null,"level":"failure-note","spans":[],"children":[],"rendered":"\u001b[0m\u001b[1m\u001b[38;5;15mFor more information about this error, try `rustc --explain E0308`.\u001b[0m\n"}
