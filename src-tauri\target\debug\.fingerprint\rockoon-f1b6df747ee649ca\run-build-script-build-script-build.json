{"rustc": 10895048813736897673, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[13568641959108680238, "build_script_build", false, 2612544852713393538], [14039947826026167952, "build_script_build", false, 2516862972154290618], [14525517306681678134, "build_script_build", false, 7234422282979917186], [16171925541490437305, "build_script_build", false, 17925086216199109804], [6828091393945420899, "build_script_build", false, 11151400144183461066], [4972584477725338812, "build_script_build", false, 8041703944481642305], [4843756016100095416, "build_script_build", false, 7725615127773020725], [1636925107938680435, "build_script_build", false, 2191077026646455243]], "local": [{"RerunIfChanged": {"output": "debug\\build\\rockoon-f1b6df747ee649ca\\output", "paths": ["tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}