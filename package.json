{"name": "rockoon", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "tauri": "cross-env RUST_LOG=info tauri", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock,build}/**/*.{vue,js,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,ts,json,tsx,css,scss,vue,html,md}\"", "lint:stylelint": "stylelint --cache --fix \"**/*.{html,vue,css,scss}\" --cache-location node_modules/.cache/stylelint/", "lint": "pnpm lint:eslint && pnpm lint:prettier && pnpm lint:stylelint", "icon": "tauri icon public/logo.png", "update-all": "pnpm update && cd src-tauri && cargo update"}, "dependencies": {"@tauri-apps/api": "^2.6.0", "@tauri-apps/plugin-dialog": "^2.3.0", "@tauri-apps/plugin-http": "^2.5.0", "@tauri-apps/plugin-positioner": "^2.3.0", "@tauri-apps/plugin-shell": "^2.3.0", "@tauri-apps/plugin-updater": "^2.9.0", "@tauri-apps/plugin-upload": "^2.3.0", "mingcute_icon": "^2.9.6", "pinia": "^2.3.1", "vue": "^3.5.17"}, "devDependencies": {"@tauri-apps/cli": "^2.6.2", "@types/node": "^22.16.4", "@vitejs/plugin-vue": "^5.2.4", "cross-env": "^7.0.3", "eslint": "^9.31.0", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-vue": "^9.33.0", "prettier": "^3.6.2", "sass-embedded": "^1.89.2", "stylelint": "^16.21.1", "stylelint-config-recess-order": "^5.1.1", "stylelint-config-recommended-vue": "^1.6.1", "stylelint-config-standard-scss": "^14.0.0", "stylelint-prettier": "^5.0.3", "typescript": "~5.6.3", "vite": "^6.3.5", "vue-tsc": "^2.2.12"}}